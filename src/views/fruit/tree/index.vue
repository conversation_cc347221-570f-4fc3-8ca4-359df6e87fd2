<template>
  <div class="app-container">
    <el-form :model="listQuery" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="果树名称" prop="name">
        <el-input
          v-model="listQuery.name"
          placeholder="请输入果树名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <el-input
          v-model="listQuery.brand"
          placeholder="请输入品牌"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="品种" prop="variety">
        <el-input
          v-model="listQuery.variety"
          placeholder="请输入品种"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleCreate"
          v-hasPermi="['fruit:tree:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDeleteRows"
          v-hasPermi="['fruit:tree:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">

      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="ID" prop="id" width="80" />

      <el-table-column align="center" label="名称" prop="name" min-width="150" show-overflow-tooltip />

      <el-table-column align="center" label="图片" prop="image" width="80">
        <template slot-scope="scope">
          <el-image v-if="scope.row.image" :src="scope.row.image" :preview-src-list="[scope.row.image]" style="width: 40px; height: 40px" fit="cover" />
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="价格" prop="price" width="100">
        <template slot-scope="scope">
          ¥{{ scope.row.price }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="运费" prop="freightPrice" width="80">
        <template slot-scope="scope">
          ¥{{ scope.row.freightPrice }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="总库存" prop="totalStock" width="100" />

      <el-table-column align="center" label="已售" prop="soldStock" width="80" />

      <el-table-column align="center" label="品牌" prop="brand" width="120" show-overflow-tooltip />

      <el-table-column align="center" label="品种" prop="variety" width="100" show-overflow-tooltip />

      <el-table-column align="center" label="规格包装" prop="specPackage" width="150" show-overflow-tooltip />

      <el-table-column align="center" label="认养周期" prop="period" width="100">
        <template slot-scope="scope">
          {{ scope.row.period }}个月
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" prop="marketEnable" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.marketEnable === 'UPPER' ? 'success' : 'danger'">
            {{ scope.row.marketEnable === 'UPPER' ? '已上架' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="创建时间" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime ? parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') : '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="320" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)" v-hasPermi="['fruit:tree:edit']">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.marketEnable === 'DOWN'" size="mini" type="text" @click="handleUp(scope.row)" v-hasPermi="['fruit:tree:edit']">上架</el-button>
          <el-button v-if="scope.row.marketEnable === 'UPPER'" size="mini" type="text" @click="handleUnder(scope.row)" v-hasPermi="['fruit:tree:edit']">下架</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)" v-hasPermi="['fruit:tree:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <!-- 果树详情对话框 -->
    <el-dialog title="果树详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="名称">{{ detailData.name }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ detailData.brand }}</el-descriptions-item>
        <el-descriptions-item label="品种">{{ detailData.variety }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ detailData.price }}</el-descriptions-item>
        <el-descriptions-item label="总库存">{{ detailData.totalStock }}</el-descriptions-item>
        <el-descriptions-item label="已售">{{ detailData.soldStock }}</el-descriptions-item>
        <el-descriptions-item label="认养周期">{{ detailData.period }}个月</el-descriptions-item>
        <el-descriptions-item label="运费">¥{{ detailData.freightPrice }}</el-descriptions-item>
        <el-descriptions-item label="产品类型">{{ detailData.productType }}</el-descriptions-item>
        <el-descriptions-item label="产地">{{ detailData.origin }}</el-descriptions-item>
        <el-descriptions-item label="规格包装">{{ detailData.specPackage }}</el-descriptions-item>
        <el-descriptions-item label="存储条件">{{ detailData.storageCondition }}</el-descriptions-item>
        <el-descriptions-item label="保质期">{{ detailData.shelflife }}</el-descriptions-item>
        <el-descriptions-item label="单果直径">{{ detailData.diameter }}</el-descriptions-item>
        <el-descriptions-item label="日照时间">{{ detailData.sunshineHours ? detailData.sunshineHours + '小时/天' : '-' }}</el-descriptions-item>
        <el-descriptions-item label="发货时间">{{ detailData.shippingTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createTime ? parseTime(detailData.createTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailData.updateTime ? parseTime(detailData.updateTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailData.createBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新人">{{ detailData.updateBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailData.description }}</el-descriptions-item>
        <el-descriptions-item label="认养权益" :span="2">
          <div v-if="detailData.benefits">
            <div v-for="(benefit, index) in detailData.benefits.split('\n')" :key="index">{{ benefit }}</div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div v-if="detailData.image" style="margin-top: 20px;">
        <h4>主图片</h4>
        <el-image :src="detailData.image" style="width: 200px; height: 200px" fit="cover" />
      </div>
      <div v-if="detailData.bannerImages" style="margin-top: 20px;">
        <h4>轮播图片</h4>
        <el-image
          v-for="(img, index) in detailData.bannerImages.split(',')"
          :key="index"
          :src="img.trim()"
          style="width: 100px; height: 100px; margin-right: 10px"
          fit="cover"
        />
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listFruitTree, getFruitTree, delFruitTree, upFruitTree, underFruitTree } from '@/api/fruit/tree'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'FruitTreeList',
  components: { Pagination },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 果树表格数据
      list: [],
      // 查询参数
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        brand: undefined,
        variety: undefined
      },
      // 详情对话框
      detailVisible: false,
      detailData: {}
    }
  },
  created() {
    this.getList()
    // 监听刷新事件
    this.$eventBus.$on('fruitTreeListRefresh', this.getList)
  },
  beforeDestroy() {
    // 移除事件监听器
    this.$eventBus.$off('fruitTreeListRefresh', this.getList)
  },
  methods: {
    parseTime,
    /** 查询果树列表 */
    getList() {
      this.loading = true
      listFruitTree(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleFilter() {
      this.listQuery.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleFilter()
    },
    handleCreate() {
      this.$router.push({ path: '/tree/add' })
    },
    handleUpdate(row) {
      this.$router.push({ path: '/tree/edit', query: { id: row.id }})
    },
    handleDetail(row) {
      getFruitTree(row.id).then(response => {
        this.detailData = response.data
        this.detailVisible = true
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除果树"' + row.name + '"吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFruitTree(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    handleUp(row) {
      this.$confirm('确定上架果树"' + row.name + '"吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        upFruitTree(row.id).then(() => {
          this.$message.success('上架成功')
          this.getList()
        }).catch(() => {
          this.$message.error('上架失败')
        })
      }).catch(() => {})
    },
    handleUnder(row) {
      this.$confirm('确定下架果树"' + row.name + '"吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        underFruitTree(row.id).then(() => {
          this.$message.success('下架成功')
          this.getList()
        }).catch(() => {
          this.$message.error('下架失败')
        })
      }).catch(() => {})
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 批量删除操作 */
    handleDeleteRows() {
      const ids = this.ids
      this.$modal.confirm('是否确认删除果树编号为"' + ids + '"的数据项？').then(function() {
        return delFruitTree(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    }
  }
}
</script>


